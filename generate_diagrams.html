<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能助手项目 - 流程图生成器</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script src="https://html2canvas.herokuapp.com/dist/html2canvas.min.js"></script>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .diagram-section {
        margin: 30px 0;
        padding: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        background: #fafafa;
      }
      .diagram-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        text-align: center;
      }
      .mermaid {
        text-align: center;
        background: white;
        padding: 20px;
        border-radius: 5px;
        margin: 10px 0;
      }
      .download-btn {
        background: #4caf50;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px 5px;
        font-size: 14px;
      }
      .download-btn:hover {
        background: #45a049;
      }
      .download-all {
        background: #2196f3;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin: 20px 0;
        display: block;
        width: 200px;
        margin: 20px auto;
      }
      .download-all:hover {
        background: #1976d2;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #2c3e50;
        font-size: 32px;
        margin-bottom: 10px;
      }
      .header p {
        color: #7f8c8d;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🤖 智能助手项目流程图生成器</h1>
        <p>基于讯飞星火4.0Ultra的多功能智能助手 - 技术架构可视化</p>
        <div style="margin: 20px 0">
          <button class="download-all" onclick="downloadAllDiagrams()">
            📥 下载所有图表
          </button>
          <button
            class="download-all"
            onclick="saveToStaticImages()"
            style="background: #ff9800; margin-left: 10px"
          >
            💾 保存到项目目录
          </button>
        </div>
        <div
          style="
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
          "
        >
          <h3 style="margin-top: 0; color: #1976d2">📋 使用说明</h3>
          <ul style="margin: 10px 0; padding-left: 20px">
            <li>
              <strong>下载所有图表</strong
              >：将所有流程图保存为PNG文件到下载文件夹
            </li>
            <li>
              <strong>保存到项目目录</strong
              >：将图表保存到static/images目录（需要手动复制）
            </li>
            <li><strong>单独下载</strong>：点击每个图表下方的"下载此图"按钮</li>
            <li>
              <strong>PPT使用</strong
              >：下载的图片可直接插入到PowerPoint演示文稿中
            </li>
          </ul>
        </div>
      </div>

      <!-- 系统架构流程图 -->
      <div class="diagram-section">
        <div class="diagram-title">🏗️ 系统架构流程图</div>
        <div class="mermaid" id="system-architecture">
          graph TB A[用户界面 Streamlit] --> B[功能路由模块] B --> C[聊天机器人]
          B --> D[知识库问答] B --> E[智能翻译] B --> F[智能客服] B -->
          G[竞赛信息提取] C --> H[讯飞星火4.0Ultra] D --> I[ChromaDB向量库] D
          --> J[text2vec嵌入模型] E --> H F --> H G --> H I --> K[本地文档存储]
          J --> L[GPU/CPU计算] style A fill:#e1f5fe style H fill:#fff3e0 style I
          fill:#f3e5f5 style J fill:#e8f5e8
        </div>
        <button
          class="download-btn"
          onclick="downloadDiagram('system-architecture', '系统架构流程图')"
        >
          下载此图
        </button>
      </div>

      <!-- 安全机制流程图 -->
      <div class="diagram-section">
        <div class="diagram-title">🛡️ 安全机制流程图</div>
        <div class="mermaid" id="security-flow">
          graph LR A[用户输入] --> B{输入验证} B -->|长度检查| C{≤3500字符?} C
          -->|否| D[智能预处理] C -->|是| E[超时装饰器] D --> E E --> F[LLM调用]
          F --> G{调用成功?} G -->|是| H[返回结果] G -->|否| I[异常处理] I -->
          J[优雅降级] style B fill:#ffebee style E fill:#e3f2fd style I
          fill:#fff3e0
        </div>
        <button
          class="download-btn"
          onclick="downloadDiagram('security-flow', '安全机制流程图')"
        >
          下载此图
        </button>
      </div>

      <!-- RAG知识库问答流程图 -->
      <div class="diagram-section">
        <div class="diagram-title">📚 RAG知识库问答流程图</div>
        <div class="mermaid" id="rag-flow">
          graph TD A[用户上传文档] --> B[文档解析] B --> C[文本分割<br />chunk_size=500]
          C --> D[text2vec嵌入<br />GPU加速] D --> E[ChromaDB存储] F[用户提问]
          --> G[问题嵌入] G --> H[向量相似性检索<br />top_k=3] H -->
          I[上下文构建] I --> J[讯飞星火生成答案] J --> K[返回结果] E --> H
          style D fill:#e8f5e8 style E fill:#f3e5f5 style J fill:#fff3e0
        </div>
        <button
          class="download-btn"
          onclick="downloadDiagram('rag-flow', 'RAG知识库问答流程图')"
        >
          下载此图
        </button>
      </div>

      <!-- 竞赛信息提取流程图 -->
      <div class="diagram-section">
        <div class="diagram-title">🏆 竞赛信息提取流程图</div>
        <div class="mermaid" id="competition-flow">
          graph TD A[原始文档/文本] --> B[格式清理<br />去除多余空格] B -->
          C[关键词筛选<br />竞赛/比赛/报名/奖项] C --> D{长度检查<br />≤3500字符?}
          D -->|否| E[智能截取<br />保留70%空间] D -->|是| F[构建提示词] E --> F
          F --> G[讯飞星火LLM<br />结构化提取] G --> H[JSON格式验证] H -->
          I{格式正确?} I -->|否| J[JSON修复<br />去除markdown/单引号] I -->|是|
          K[返回结构化数据] J --> K style C fill:#e8f5e8 style G fill:#fff3e0
          style H fill:#f3e5f5
        </div>
        <button
          class="download-btn"
          onclick="downloadDiagram('competition-flow', '竞赛信息提取流程图')"
        >
          下载此图
        </button>
      </div>

      <!-- 四层技术架构图 -->
      <div class="diagram-section">
        <div class="diagram-title">🏗️ 四层技术架构图</div>
        <div class="mermaid" id="tech-architecture">
          graph TB subgraph "前端层 - Streamlit" A[用户界面] B[功能路由]
          C[交互组件] end subgraph "业务层 - Utils模块" D[config.py<br />配置管理]
          E[knowledge_base.py<br />知识库服务] F[competition.py<br />信息提取]
          G[translator.py<br />翻译服务] H[timeout.py<br />超时控制] end
          subgraph "服务层 - AI服务" I[讯飞星火4.0Ultra<br />大语言模型]
          J[text2vec-base-chinese<br />嵌入模型] K[ChromaDB<br />向量数据库] end
          subgraph "数据层 - 存储" L[data/knowledge<br />文档存储]
          M[data/vector_db<br />向量存储] N[local_models<br />模型文件] end A
          --> B B --> D B --> E B --> F B --> G D --> I E --> J E --> K F --> I
          G --> I H --> I J --> N K --> M E --> L style A fill:#e1f5fe style I
          fill:#fff3e0 style K fill:#f3e5f5 style J fill:#e8f5e8
        </div>
        <button
          class="download-btn"
          onclick="downloadDiagram('tech-architecture', '四层技术架构图')"
        >
          下载此图
        </button>
      </div>

      <!-- 技术成果雷达图 -->
      <div class="diagram-section">
        <div class="diagram-title">📊 技术成果与创新点</div>
        <div class="mermaid" id="tech-achievements">
          graph LR subgraph "技术成果指标" A[功能完整性<br />5/5模块]
          B[性能优化<br />GPU加速] C[安全机制<br />120s超时] D[兼容性<br />跨平台]
          E[部署便利<br />本地化] end subgraph "技术创新亮点" F[智能预处理<br />3500字符优化]
          G[多编码支持<br />UTF-8/GBK自适应] H[装饰器超时<br />跨平台兼容]
          I[RAG架构<br />向量检索+生成] J[模块化设计<br />松耦合架构] end A -->
          F B --> G C --> H D --> I E --> J style A fill:#4caf50 style B
          fill:#2196f3 style C fill:#ff9800 style D fill:#9c27b0 style E
          fill:#f44336
        </div>
        <button
          class="download-btn"
          onclick="downloadDiagram('tech-achievements', '技术成果与创新点')"
        >
          下载此图
        </button>
      </div>
    </div>

    <script>
      // 初始化Mermaid
      mermaid.initialize({
        startOnLoad: true,
        theme: "default",
        themeVariables: {
          primaryColor: "#4CAF50",
          primaryTextColor: "#333",
          primaryBorderColor: "#2196F3",
          lineColor: "#666",
          secondaryColor: "#f8f9fa",
          tertiaryColor: "#e9ecef",
        },
      });

      // 下载单个图表
      function downloadDiagram(elementId, filename) {
        const element = document.getElementById(elementId);
        html2canvas(element, {
          backgroundColor: "#ffffff",
          scale: 2,
          useCORS: true,
        }).then((canvas) => {
          const link = document.createElement("a");
          link.download = filename + ".png";
          link.href = canvas.toDataURL();
          link.click();
        });
      }

      // 下载所有图表
      function downloadAllDiagrams() {
        const diagrams = [
          { id: "system-architecture", name: "系统架构流程图" },
          { id: "security-flow", name: "安全机制流程图" },
          { id: "rag-flow", name: "RAG知识库问答流程图" },
          { id: "competition-flow", name: "竞赛信息提取流程图" },
          { id: "tech-architecture", name: "四层技术架构图" },
          { id: "tech-achievements", name: "技术成果与创新点" },
        ];

        diagrams.forEach((diagram, index) => {
          setTimeout(() => {
            downloadDiagram(diagram.id, diagram.name);
          }, index * 1000); // 每秒下载一个，避免浏览器阻止
        });
      }

      // 保存到项目static/images目录（提示用户手动复制）
      function saveToStaticImages() {
        alert(
          "即将下载所有图表，请将下载的PNG文件手动复制到项目的 static/images/ 目录中，用于PPT制作。"
        );
        downloadAllDiagrams();

        // 显示复制指南
        setTimeout(() => {
          const guide = `
📋 文件复制指南：

1. 下载完成后，在下载文件夹中找到以下文件：
   - 系统架构流程图.png
   - 安全机制流程图.png
   - RAG知识库问答流程图.png
   - 竞赛信息提取流程图.png
   - 四层技术架构图.png
   - 技术成果与创新点.png

2. 将这些文件复制到项目目录：
   static/images/

3. 重命名为英文文件名（可选）：
   - system_architecture.png
   - security_flow.png
   - rag_flow.png
   - competition_flow.png
   - tech_architecture.png
   - tech_achievements.png

4. 现在可以在PPT中引用这些图片了！
          `;

          if (confirm(guide + "\n\n点击确定复制指南到剪贴板")) {
            navigator.clipboard
              .writeText(guide)
              .then(() => {
                alert("复制指南已复制到剪贴板！");
              })
              .catch(() => {
                console.log("复制指南：", guide);
              });
          }
        }, 6000); // 等待所有下载完成
      }

      // 页面加载完成后的处理
      window.addEventListener("load", function () {
        console.log("所有图表已生成完成！");
      });
    </script>
  </body>
</html>
