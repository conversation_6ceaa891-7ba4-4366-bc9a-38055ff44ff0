<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能助手项目PPT演示</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
      }

      .slide-container {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      .slide {
        width: 90%;
        max-width: 1200px;
        height: 80vh;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        padding: 40px;
        box-sizing: border-box;
        display: none;
        overflow-y: auto;
      }

      .slide.active {
        display: block;
      }

      .slide h1 {
        color: #2c3e50;
        font-size: 2.5em;
        margin-bottom: 20px;
        text-align: center;
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
      }

      .slide h2 {
        color: #34495e;
        font-size: 1.8em;
        margin: 25px 0 15px 0;
      }

      .slide h3 {
        color: #2980b9;
        font-size: 1.4em;
        margin: 20px 0 10px 0;
      }

      .content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin: 20px 0;
      }

      .feature-list {
        list-style: none;
        padding: 0;
      }

      .feature-list li {
        background: #ecf0f1;
        margin: 10px 0;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #3498db;
      }

      .tech-stack {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border: 2px solid #e9ecef;
      }

      .code-block {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 20px;
        border-radius: 8px;
        font-family: "Courier New", monospace;
        font-size: 0.9em;
        overflow-x: auto;
        margin: 15px 0;
      }

      .highlight {
        background: #f39c12;
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
      }

      .navigation {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 10px;
        z-index: 1000;
      }

      .nav-btn {
        background: #3498db;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s;
      }

      .nav-btn:hover {
        background: #2980b9;
        transform: translateY(-2px);
      }

      .nav-btn:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
        transform: none;
      }

      .slide-number {
        position: absolute;
        top: 20px;
        right: 30px;
        background: #34495e;
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 14px;
      }

      .image-placeholder {
        background: #ecf0f1;
        border: 2px dashed #bdc3c7;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        color: #7f8c8d;
        margin: 20px 0;
      }

      .demo-section {
        background: #e8f5e8;
        padding: 20px;
        border-radius: 10px;
        border-left: 5px solid #27ae60;
        margin: 20px 0;
      }

      .metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .metric-card {
        background: #3498db;
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
      }

      .metric-number {
        font-size: 2em;
        font-weight: bold;
        display: block;
      }

      .timer {
        position: fixed;
        top: 30px;
        left: 30px;
        background: #e74c3c;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 18px;
        font-weight: bold;
        z-index: 1000;
      }
    </style>
  </head>
  <body>
    <div class="timer" id="timer">⏱️ 00:00</div>

    <!-- 第1页：项目概述 -->
    <div class="slide-container">
      <div class="slide active">
        <div class="slide-number">1/10</div>
        <h1>🤖 智能助手项目概览</h1>

        <div class="content-grid">
          <div>
            <h2>项目定位</h2>
            <div class="tech-stack">
              <h3>基于讯飞星火4.0Ultra的多功能智能助手</h3>
              <ul class="feature-list">
                <li>
                  <strong>技术栈：</strong>Streamlit + LangChain + ChromaDB +
                  PyTorch
                </li>
                <li><strong>核心特色：</strong>本地化部署 + 云端大模型</li>
                <li><strong>部署方式：</strong>跨平台兼容，离线可用</li>
              </ul>
            </div>
          </div>

          <div>
            <h2>五大核心功能</h2>
            <ul class="feature-list">
              <li>💬 <strong>聊天机器人</strong> - 上下文记忆对话</li>
              <li>📚 <strong>知识库问答</strong> - RAG架构检索</li>
              <li>🌐 <strong>智能翻译</strong> - 多语言支持</li>
              <li>🎧 <strong>智能客服</strong> - 分类问题处理</li>
              <li>🏆 <strong>竞赛信息提取</strong> - 结构化输出</li>
            </ul>
          </div>
        </div>

        <div class="image-placeholder">
          📊 系统架构流程图
          <br /><small>（static/images/system_architecture.png）</small>
        </div>
      </div>
    </div>

    <!-- 第2页：系统架构 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">2/10</div>
        <h1>🏗️ 系统架构设计</h1>

        <div class="content-grid">
          <div>
            <h2>四层架构设计</h2>
            <div class="tech-stack">
              <h3>🎨 前端层 - Streamlit</h3>
              <p>响应式Web界面，交互组件</p>

              <h3>⚙️ 业务层 - Utils模块</h3>
              <p>模块化工具库，功能解耦</p>

              <h3>🤖 服务层 - AI服务</h3>
              <p>讯飞星火 + text2vec + ChromaDB</p>

              <h3>💾 数据层 - 存储</h3>
              <p>本地文档 + 向量数据库</p>
            </div>
          </div>

          <div>
            <h2>核心代码架构</h2>
            <div class="code-block">
              # app.py - 主应用架构 app_mode = st.radio("选择功能模式:",
              ["聊天机器人", "智能翻译", "智能客服", "知识库问答",
              "竞赛信息提取"]) # 统一的功能路由设计 if app_mode == "聊天机器人":
              llm = get_spark_llm() response = llm(prompt) elif app_mode ==
              "知识库问答": response = query_knowledge_base(prompt, vectordb)
            </div>
          </div>
        </div>

        <div class="image-placeholder">
          🏗️ 四层技术架构图
          <br /><small>（static/images/tech_architecture.png）</small>
        </div>
      </div>
    </div>

    <!-- 第3页：核心功能展示 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">3/10</div>
        <h1>⚡ 核心功能展示</h1>

        <div class="content-grid">
          <div>
            <h2>功能特性</h2>
            <ul class="feature-list">
              <li>
                <strong>💬 聊天机器人</strong
                ><br />基于讯飞星火4.0Ultra，支持上下文记忆
              </li>
              <li>
                <strong>📚 知识库问答</strong><br />RAG架构，文档向量化检索
              </li>
              <li><strong>🌐 智能翻译</strong><br />多语言支持，上下文理解</li>
              <li><strong>🎧 智能客服</strong><br />分类问题处理，专业回答</li>
              <li>
                <strong>🏆 竞赛信息提取</strong><br />智能解析，结构化输出
              </li>
            </ul>
          </div>

          <div>
            <h2>技术指标</h2>
            <div class="metrics">
              <div class="metric-card">
                <span class="metric-number">&lt;5s</span>
                响应时间
              </div>
              <div class="metric-card">
                <span class="metric-number">&gt;85%</span>
                问答准确率
              </div>
              <div class="metric-card">
                <span class="metric-number">5</span>
                功能模块
              </div>
              <div class="metric-card">
                <span class="metric-number">120s</span>
                超时保护
              </div>
            </div>
          </div>
        </div>

        <div class="image-placeholder">
          📱 功能界面截图组合
          <br /><small
            >（chat_function.png + knowledge_qa.png +
            competition_extract.png）</small
          >
        </div>
      </div>
    </div>

    <!-- 第4页：技术亮点 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">4/10</div>
        <h1>🚀 技术创新亮点</h1>

        <div class="content-grid">
          <div>
            <h2>四大技术亮点</h2>
            <ul class="feature-list">
              <li>
                <strong>🔒 超时控制</strong><br />120秒全局保护，跨平台兼容
              </li>
              <li>
                <strong>🚀 本地模型</strong><br />text2vec-base-chinese，GPU加速
              </li>
              <li>
                <strong>📊 智能解析</strong><br />3500字符限制，智能预处理
              </li>
              <li><strong>🛡️ 容错机制</strong><br />多层异常处理，优雅降级</li>
            </ul>

            <div class="demo-section">
              <h3>🔧 核心代码展示</h3>
              <div class="code-block">
                # utils/timeout.py - 跨平台超时装饰器 @timeout(seconds=120) def
                safe_llm_call(prompt): if len(prompt) > MAX_INPUT_LENGTH: raise
                ValueError(f"输入长度超过{MAX_INPUT_LENGTH}字符限制") llm =
                get_spark_llm() return llm.invoke(prompt)
              </div>
            </div>
          </div>

          <div>
            <div class="image-placeholder">
              🛡️ 安全机制流程图
              <br /><small>（static/images/security_flow.png）</small>
            </div>

            <h2>安全机制流程</h2>
            <div class="tech-stack">
              <p>
                <strong>输入验证</strong> → <strong>长度检查</strong> →
                <strong>超时装饰器</strong> → <strong>LLM调用</strong> →
                <strong>异常处理</strong> → <strong>优雅降级</strong>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第5页：RAG知识库架构 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">5/10</div>
        <h1>📚 RAG知识库问答系统</h1>

        <div class="content-grid">
          <div>
            <h2>RAG架构设计</h2>
            <div class="tech-stack">
              <h3>📄 文档处理阶段</h3>
              <ul>
                <li>支持PDF/Word/TXT/MD格式</li>
                <li>文本分割：chunk_size=500</li>
                <li>向量化：text2vec-base-chinese</li>
                <li>存储：ChromaDB向量数据库</li>
              </ul>

              <h3>❓ 问答阶段</h3>
              <ul>
                <li>问题嵌入向量化</li>
                <li>相似性检索：top_k=3</li>
                <li>上下文构建</li>
                <li>讯飞星火生成答案</li>
              </ul>
            </div>
          </div>

          <div>
            <div class="image-placeholder">
              📚 RAG知识库问答流程图
              <br /><small>（static/images/rag_flow.png）</small>
            </div>

            <div class="code-block">
              # utils/knowledge_base.py - 知识库初始化 def
              init_knowledge_base(): device = "cuda" if
              torch.cuda.is_available() else "cpu" embeddings =
              HuggingFaceEmbeddings( model_name=str(model_path),
              model_kwargs={"device": device} ) text_splitter =
              RecursiveCharacterTextSplitter( chunk_size=500, chunk_overlap=100
              ) return Chroma.from_documents(documents=texts,
              embedding=embeddings)
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第6页：竞赛信息提取 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">6/10</div>
        <h1>🏆 智能竞赛信息提取</h1>

        <div class="content-grid">
          <div>
            <h2>智能预处理算法</h2>
            <div class="tech-stack">
              <h3>🔍 处理流程</h3>
              <ul>
                <li><strong>格式清理</strong>：去除多余空格</li>
                <li><strong>关键词筛选</strong>：竞赛/比赛/报名/奖项</li>
                <li><strong>长度控制</strong>：保留70%空间</li>
                <li><strong>结构化提取</strong>：JSON格式输出</li>
              </ul>

              <h3>📊 性能指标</h3>
              <div class="metrics">
                <div class="metric-card">
                  <span class="metric-number">&gt;90%</span>
                  提取准确率
                </div>
                <div class="metric-card">
                  <span class="metric-number">3500</span>
                  字符限制
                </div>
              </div>
            </div>
          </div>

          <div>
            <div class="image-placeholder">
              🏆 竞赛信息提取流程图
              <br /><small>（static/images/competition_flow.png）</small>
            </div>

            <div class="code-block">
              # utils/competition.py - 智能预处理 def preprocess_text(text):
              text = re.sub(r'\s+', ' ', text).strip() key_sections = [] for
              line in text.split('。'): if any(keyword in line for keyword in
              ["竞赛", "比赛", "报名", "奖项", "主办"]):
              key_sections.append(line) if len('。'.join(key_sections)) >
              MAX_INPUT_LENGTH * 0.7: break return
              '。'.join(key_sections)[:MAX_INPUT_LENGTH]
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第7页：性能与安全 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">7/10</div>
        <h1>🛡️ 性能优化与安全机制</h1>

        <div class="content-grid">
          <div>
            <h2>性能优化</h2>
            <ul class="feature-list">
              <li>
                <strong>🚀 GPU加速计算</strong><br />CUDA支持，自动设备检测
              </li>
              <li>
                <strong>💾 向量数据库持久化</strong><br />ChromaDB本地存储
              </li>
              <li>
                <strong>⚡ 懒加载模式</strong><br />CUDA_MODULE_LOADING=LAZY
              </li>
              <li>
                <strong>📄 文档分块处理</strong><br />chunk_size=500,
                overlap=100
              </li>
            </ul>

            <h2>安全机制</h2>
            <ul class="feature-list">
              <li><strong>⏱️ 全局超时保护</strong><br />120秒网络超时控制</li>
              <li><strong>✅ 输入验证</strong><br />3500字符长度限制</li>
              <li><strong>🔒 隐私保护</strong><br />禁用分析追踪</li>
              <li><strong>🛠️ 错误处理</strong><br />多层异常捕获</li>
            </ul>
          </div>

          <div>
            <div class="image-placeholder">
              📊 技术成果与创新点
              <br /><small>（static/images/tech_achievements.png）</small>
            </div>

            <div class="code-block">
              # 全局安全配置 os.environ["STREAMLIT_ANALYTICS_ENABLED"] = "false"
              os.environ["CUDA_MODULE_LOADING"] = "LAZY" NETWORK_TIMEOUT = 120
              socket.setdefaulttimeout(NETWORK_TIMEOUT) # 性能优化 device =
              "cuda" if torch.cuda.is_available() else "cpu" embeddings =
              HuggingFaceEmbeddings(model_kwargs={"device": device})
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第8页：部署与扩展 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">8/10</div>
        <h1>🚀 部署方案与未来规划</h1>

        <div class="content-grid">
          <div>
            <h2>部署优势</h2>
            <div class="tech-stack">
              <h3>🏠 本地化部署</h3>
              <ul>
                <li>数据安全，隐私保护</li>
                <li>离线可用，无网络依赖</li>
                <li>完全控制，自主可控</li>
              </ul>

              <h3>🌐 跨平台兼容</h3>
              <ul>
                <li>Windows/Linux/macOS支持</li>
                <li>Python 3.9+环境</li>
                <li>CUDA可选，CPU也可运行</li>
              </ul>

              <h3>📦 标准化安装</h3>
              <ul>
                <li>requirements.txt依赖管理</li>
                <li>一键启动脚本</li>
                <li>自动环境检测</li>
              </ul>
            </div>
          </div>

          <div>
            <h2>未来规划</h2>
            <div class="demo-section">
              <h3>🔮 功能扩展</h3>
              <ul>
                <li><strong>🎤 语音交互</strong>：语音输入输出</li>
                <li><strong>👁️ 图像理解</strong>：多模态处理</li>
                <li><strong>📱 移动端</strong>：APP应用开发</li>
                <li><strong>🔗 API服务</strong>：RESTful接口</li>
              </ul>

              <h3>⚡ 技术升级</h3>
              <ul>
                <li>更大规模模型集成</li>
                <li>分布式部署架构</li>
                <li>实时流式处理</li>
                <li>边缘计算支持</li>
              </ul>
            </div>

            <div class="metrics">
              <div class="metric-card">
                <span class="metric-number">5GB</span>
                最小存储需求
              </div>
              <div class="metric-card">
                <span class="metric-number">8GB</span>
                推荐内存
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第9页：项目总结 -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">9/10</div>
        <h1>📊 项目成果总结</h1>

        <div class="content-grid">
          <div>
            <h2>✅ 技术成果</h2>
            <ul class="feature-list">
              <li><strong>功能完整性</strong><br />5大功能模块100%实现</li>
              <li><strong>安全机制</strong><br />120秒超时保护全覆盖</li>
              <li><strong>兼容性</strong><br />跨平台部署设计</li>
              <li><strong>本地化</strong><br />离线可用方案</li>
            </ul>

            <h2>🚀 技术创新</h2>
            <ul class="feature-list">
              <li><strong>智能预处理</strong><br />70%压缩率算法</li>
              <li><strong>多编码支持</strong><br />4种编码自适应</li>
              <li><strong>装饰器超时</strong><br />跨平台兼容</li>
              <li><strong>RAG架构</strong><br />85%准确率问答</li>
            </ul>
          </div>

          <div>
            <h2>💰 商业价值</h2>
            <div class="tech-stack">
              <h3>降本增效</h3>
              <p>自动化处理减少人工成本，提高工作效率</p>

              <h3>数据安全</h3>
              <p>本地部署保护企业隐私，符合合规要求</p>

              <h3>快速部署</h3>
              <p>标准化安装流程，快速上线使用</p>

              <h3>持续优化</h3>
              <p>可扩展技术架构，支持功能迭代</p>
            </div>

            <div class="metrics">
              <div class="metric-card">
                <span class="metric-number">262</span>
                代码行数
              </div>
              <div class="metric-card">
                <span class="metric-number">6</span>
                工具模块
              </div>
              <div class="metric-card">
                <span class="metric-number">100%</span>
                功能覆盖
              </div>
              <div class="metric-card">
                <span class="metric-number">0</span>
                安全漏洞
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第10页：演示与Q&A -->
    <div class="slide-container">
      <div class="slide">
        <div class="slide-number">10/10</div>
        <h1>🎬 现场演示与Q&A</h1>

        <div class="content-grid">
          <div>
            <h2>🎯 演示内容</h2>
            <div class="demo-section">
              <h3>📚 知识库问答演示 (1分钟)</h3>
              <ul>
                <li>上传PDF文档</li>
                <li>向量化处理展示</li>
                <li>智能问答效果</li>
                <li>信息来源标注</li>
              </ul>

              <h3>🏆 竞赛信息提取演示 (1分钟)</h3>
              <ul>
                <li>文档解析过程</li>
                <li>关键信息识别</li>
                <li>结构化输出</li>
                <li>JSON格式展示</li>
              </ul>
            </div>

            <div class="code-block">
              # 演示命令 streamlit run app.py # 访问地址 http://localhost:8501 #
              测试文档 data/knowledge/sample.pdf
            </div>
          </div>

          <div>
            <h2>❓ Q&A环节</h2>
            <div class="tech-stack">
              <h3>常见问题</h3>
              <ul>
                <li><strong>部署要求</strong>：Python 3.9+, 8GB内存</li>
                <li><strong>GPU需求</strong>：可选，CPU也可运行</li>
                <li><strong>数据安全</strong>：完全本地化，无外网传输</li>
                <li><strong>扩展性</strong>：模块化设计，易于扩展</li>
                <li><strong>维护成本</strong>：低维护，自动化运行</li>
              </ul>

              <h3>技术支持</h3>
              <ul>
                <li>详细技术文档</li>
                <li>安装部署指南</li>
                <li>故障排除手册</li>
                <li>在线技术支持</li>
              </ul>
            </div>

            <div class="image-placeholder">
              🎯 项目演示截图
              <br /><small>（实时演示界面）</small>
            </div>
          </div>
        </div>

        <div
          style="
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
          "
        >
          <h2 style="color: #27ae60; margin: 0">🎉 谢谢大家！</h2>
          <p style="margin: 10px 0; font-size: 18px">
            基于讯飞星火4.0Ultra的多功能智能助手
          </p>
          <p style="margin: 0; color: #7f8c8d">
            技术先进 · 功能完整 · 部署便利
          </p>
        </div>
      </div>
    </div>
    <!-- 导航按钮 -->
    <div class="navigation">
      <button class="nav-btn" onclick="previousSlide()" id="prevBtn">
        ⬅️ 上一页
      </button>
      <button class="nav-btn" onclick="toggleTimer()" id="timerBtn">
        ⏱️ 开始计时
      </button>
      <button class="nav-btn" onclick="nextSlide()" id="nextBtn">
        下一页 ➡️
      </button>
    </div>

    <script>
      let currentSlide = 0;
      const slides = document.querySelectorAll(".slide");
      const totalSlides = slides.length;
      let timerInterval;
      let startTime;
      let isTimerRunning = false;

      function showSlide(n) {
        slides[currentSlide].classList.remove("active");
        currentSlide = (n + totalSlides) % totalSlides;
        slides[currentSlide].classList.add("active");

        // 更新导航按钮状态
        document.getElementById("prevBtn").disabled = currentSlide === 0;
        document.getElementById("nextBtn").disabled =
          currentSlide === totalSlides - 1;
      }

      function nextSlide() {
        if (currentSlide < totalSlides - 1) {
          showSlide(currentSlide + 1);
        }
      }

      function previousSlide() {
        if (currentSlide > 0) {
          showSlide(currentSlide - 1);
        }
      }

      function toggleTimer() {
        const timerBtn = document.getElementById("timerBtn");
        const timerDisplay = document.getElementById("timer");

        if (!isTimerRunning) {
          startTime = Date.now();
          isTimerRunning = true;
          timerBtn.textContent = "⏹️ 停止计时";
          timerBtn.style.background = "#e74c3c";

          timerInterval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            timerDisplay.textContent = `⏱️ ${minutes
              .toString()
              .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

            // 5分钟提醒
            if (elapsed >= 300000 && elapsed < 301000) {
              alert("⏰ 演讲时间已达5分钟！");
            }
          }, 1000);
        } else {
          clearInterval(timerInterval);
          isTimerRunning = false;
          timerBtn.textContent = "⏱️ 开始计时";
          timerBtn.style.background = "#3498db";
        }
      }

      // 键盘导航
      document.addEventListener("keydown", (e) => {
        if (e.key === "ArrowRight" || e.key === " ") {
          nextSlide();
        } else if (e.key === "ArrowLeft") {
          previousSlide();
        } else if (e.key === "Escape") {
          toggleTimer();
        }
      });

      // 初始化
      showSlide(0);
    </script>
  </body>
</html>
