# 智能助手项目使用指南

## 📋 项目文件说明

### 核心文件
- **app.py** - 主应用程序，包含所有功能模块
- **requirements.txt** - 项目依赖包列表
- **torch_patch.py** - PyTorch补丁文件

### 工具模块 (utils/)
- **config.py** - 配置管理，讯飞星火API设置
- **knowledge_base.py** - 知识库管理，RAG实现
- **competition.py** - 竞赛信息提取功能
- **translator.py** - 智能翻译功能
- **timeout.py** - 超时控制装饰器

### 数据目录 (data/)
- **knowledge/** - 知识文档存储
- **vector_db/** - 向量数据库存储
- **competitions/** - 竞赛数据存储

### 静态资源 (static/)
- **styles/style.css** - 自定义样式文件
- **images/** - 项目截图和图片资源

### 演示文件
- **generate_diagrams.html** - 流程图生成器
- **智能助手项目PPT演示.html** - HTML版PPT演示
- **智能助手项目演示PPT.md** - PPT制作大纲
- **智能助手项目演讲稿.md** - 5分钟演讲稿
- **技术栈文档.md** - 详细技术文档

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Python 3.9+
python --version

# 安装依赖
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 配置环境变量
创建 `.env` 文件：
```bash
SPARK_APP_ID=your_app_id
SPARK_API_KEY=your_api_key
SPARK_API_SECRET=your_api_secret
```

### 3. 启动应用
```bash
streamlit run app.py
```

### 4. 访问应用
打开浏览器访问：http://localhost:8501

## 📊 功能使用说明

### 聊天机器人
1. 选择"聊天机器人"模式
2. 在输入框中输入问题
3. 系统基于讯飞星火4.0Ultra生成回答
4. 支持上下文记忆对话

### 知识库问答
1. 选择"知识库问答"模式
2. 在侧边栏上传文档（PDF/Word/TXT/MD）
3. 等待文档向量化处理完成
4. 提问关于文档内容的问题
5. 系统基于RAG架构返回答案

### 智能翻译
1. 选择"智能翻译"模式
2. 输入要翻译的文本
3. 选择目标语言
4. 点击"翻译"按钮获取结果

### 智能客服
1. 选择"智能客服"模式
2. 选择问题分类
3. 输入具体问题
4. 获取专业领域回答

### 竞赛信息提取
1. 选择"竞赛信息提取"模式
2. 上传竞赛文档或粘贴文本
3. 点击"提取信息"
4. 获取结构化JSON输出

## 🎨 演示文件使用

### 流程图生成器
1. 打开 `generate_diagrams.html`
2. 查看所有技术架构图
3. 点击"下载所有图表"保存PNG文件
4. 点击"保存到项目目录"获取复制指南

### HTML版PPT演示
1. 打开 `智能助手项目PPT演示.html`
2. 使用导航按钮或键盘方向键切换页面
3. 点击"开始计时"进行5分钟演讲计时
4. 支持全屏演示模式

### 演讲稿使用
1. 参考 `智能助手项目演讲稿.md`
2. 按照时间分配进行练习
3. 重点记忆关键技术数据
4. 准备现场演示环节

## 🔧 技术参数配置

### 超时设置
- 全局网络超时：120秒
- LLM调用超时：120秒
- 文件上传超时：60秒

### 文本处理
- 最大输入长度：3500字符
- 文档分块大小：500字符
- 分块重叠：100字符
- 向量检索数量：top_k=3

### 性能优化
- GPU自动检测和使用
- 向量数据库持久化
- 懒加载模式启用
- 分析追踪禁用

## 📱 项目截图指南

### 需要截图的界面
1. **主界面** - 显示五大功能选择
2. **聊天机器人** - 对话界面和回答效果
3. **知识库问答** - 文档上传和问答过程
4. **智能翻译** - 翻译界面和结果
5. **智能客服** - 客服分类和回答
6. **竞赛信息提取** - 文档解析和JSON输出

### 截图要求
- 分辨率：1920x1080
- 格式：PNG
- 内容：完整功能界面
- 保存位置：static/images/

## 🎯 演示准备清单

### 技术准备
- [ ] 确认项目正常运行
- [ ] 准备测试文档（PDF/Word）
- [ ] 检查网络连接稳定
- [ ] 测试所有功能模块

### 演示准备
- [ ] 熟悉演讲稿内容
- [ ] 练习时间控制（5分钟）
- [ ] 准备Q&A环节回答
- [ ] 检查演示设备

### 文件准备
- [ ] 下载所有流程图
- [ ] 准备PPT演示文件
- [ ] 打印演讲稿备份
- [ ] 准备技术文档

## 🔍 故障排除

### 常见问题
1. **启动失败**：检查Python版本和依赖安装
2. **API调用失败**：检查环境变量配置
3. **文档上传失败**：检查文件格式和大小
4. **向量化失败**：检查模型文件完整性
5. **超时错误**：检查网络连接和超时设置

### 解决方案
- 查看控制台错误信息
- 检查日志文件
- 重启应用程序
- 清理缓存文件
- 重新安装依赖

## 📞 技术支持

### 联系方式
- 项目文档：查看技术栈文档.md
- 代码问题：检查utils模块实现
- 部署问题：参考环境配置说明
- 功能问题：查看app.py主文件

### 扩展开发
- 添加新功能：在utils/目录创建新模块
- 修改界面：编辑app.py和static/styles/
- 集成新模型：修改config.py配置
- 数据库扩展：扩展knowledge_base.py

---

**项目版本**：基于当前代码库  
**更新时间**：2024年  
**技术支持**：智能助手项目团队
