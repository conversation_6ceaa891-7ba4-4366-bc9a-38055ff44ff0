<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程图测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .diagram-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .mermaid {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 5px;
        }
        .download-btn {
            background: #4caf50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .download-btn:hover {
            background: #45a049;
        }
        .status {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 流程图测试页面</h1>
        <p>这是一个简化的测试页面，用于验证Mermaid图表和下载功能。</p>
        
        <div class="status">
            <strong>状态：</strong><span id="status">正在加载...</span>
        </div>
        
        <button class="download-btn" onclick="testDownload()">🧪 测试下载</button>
        <button class="download-btn" onclick="checkStatus()">🔍 检查状态</button>
        
        <!-- 简单的系统架构图 -->
        <div class="diagram-section">
            <h3>🏗️ 系统架构图（简化版）</h3>
            <div class="mermaid" id="simple-architecture">
graph TB
    A[用户界面] --> B[功能路由]
    B --> C[聊天机器人]
    B --> D[知识库问答]
    B --> E[智能翻译]
    C --> F[讯飞星火]
    D --> G[ChromaDB]
    E --> F
    
    style A fill:#e1f5fe
    style F fill:#fff3e0
    style G fill:#f3e5f5
            </div>
            <button class="download-btn" onclick="downloadDiagram('simple-architecture', '简化系统架构图')">
                下载此图
            </button>
        </div>
        
        <!-- 简单的流程图 -->
        <div class="diagram-section">
            <h3>🔄 处理流程图</h3>
            <div class="mermaid" id="simple-flow">
graph LR
    A[输入] --> B{验证}
    B -->|通过| C[处理]
    B -->|失败| D[错误处理]
    C --> E[输出]
    D --> F[返回错误]
    
    style B fill:#ffebee
    style C fill:#e8f5e8
    style D fill:#fff3e0
            </div>
            <button class="download-btn" onclick="downloadDiagram('simple-flow', '简化处理流程图')">
                下载此图
            </button>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: "default",
            securityLevel: 'loose'
        });

        // 更新状态
        function updateStatus(message, isError = false) {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.style.color = isError ? '#e74c3c' : '#27ae60';
            }
            console.log('状态:', message);
        }

        // 检查状态
        function checkStatus() {
            const mermaidLoaded = typeof mermaid !== 'undefined';
            const html2canvasLoaded = typeof html2canvas !== 'undefined';
            const diagrams = document.querySelectorAll('.mermaid');
            
            let renderedCount = 0;
            diagrams.forEach(diagram => {
                if (diagram.querySelector('svg')) {
                    renderedCount++;
                }
            });
            
            const status = `Mermaid: ${mermaidLoaded ? '✅' : '❌'}, html2canvas: ${html2canvasLoaded ? '✅' : '❌'}, 图表: ${renderedCount}/${diagrams.length}`;
            updateStatus(status);
            
            console.log('详细状态:', {
                mermaidLoaded,
                html2canvasLoaded,
                totalDiagrams: diagrams.length,
                renderedDiagrams: renderedCount
            });
        }

        // 下载图表
        function downloadDiagram(elementId, filename) {
            console.log(`开始下载: ${elementId}`);
            const element = document.getElementById(elementId);
            
            if (!element) {
                alert(`找不到元素: ${elementId}`);
                return;
            }

            if (typeof html2canvas === 'undefined') {
                alert('html2canvas库未加载');
                return;
            }

            html2canvas(element, {
                backgroundColor: "#ffffff",
                scale: 2,
                useCORS: true
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = filename + '.png';
                link.href = canvas.toDataURL('image/png');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                updateStatus(`✅ ${filename} 下载完成`);
            }).catch(error => {
                console.error('下载错误:', error);
                updateStatus(`❌ 下载失败: ${error.message}`, true);
            });
        }

        // 测试下载
        function testDownload() {
            downloadDiagram('simple-architecture', '测试-系统架构图');
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            updateStatus('页面加载完成，正在检查...');
            
            setTimeout(() => {
                checkStatus();
            }, 2000);
        });
    </script>
</body>
</html>
